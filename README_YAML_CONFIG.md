# Amazon账号登录验证工具 - YAML配置版

## 概述

本工具已升级为使用YAML配置文件，替代了原来的环境变量配置方式。这样可以更方便地管理和修改配置参数。

## 快速开始

### 1. 编译程序

运行构建脚本：
```bash
build.bat
```

构建脚本会自动检测您的系统环境：
- 如果有GCC编译器：编译SQLite3版本 (`amazon_checker_sqlite.exe`)
- 如果没有GCC编译器：编译JSON版本 (`amazon_checker.exe`)

### 2. 配置文件

程序首次运行时会自动创建 `config.yaml` 配置文件。您也可以手动编辑此文件：

```yaml
# Amazon账号登录验证工具配置文件

# 数据库配置
database:
  # 数据库文件路径
  path: "db/amazon.db"
  # 数据库类型: "sqlite" 或 "json"
  type: "json"

# 工作器配置
worker:
  # 并发线程数
  num_threads: 1
  # 每批处理的账号数量
  accounts_per_batch: 1
  # 开始处理的账号索引（从0开始）
  start_index: 0
  # 代理池大小
  proxy_per_batch: 100

# 服务器配置
server:
  # 验证码识别服务器地址
  code_server: "http://*************:38787"
  # 代理API地址
  proxy_api: "https://acq.iemoapi.com/getProxyIp?lb=1&return_type=txt&protocol=http&num="

# URL配置
urls:
  # Amazon主页URL
  amazon: "https://www.amazon.com/"
  # 登录页面URL
  login: "https://www.amazon.com/ap/signin"
  # 验证码验证URL
  captcha: "https://www.amazon.com/errors/validateCaptcha"
  # CVF验证URL
  cvf_verify: "https://www.amazon.com/ap/cvf/verify"
```

### 3. 准备账号数据

#### 方法1: 使用测试数据
程序已包含测试账号数据 (`db/db_account.json`)，可直接运行测试。

#### 方法2: 迁移现有数据
如果您有原始的JSON账号文件，可以使用迁移脚本：
```bash
python migrate_data.py db/amazon.db db/your_account_file.json
```

### 4. 运行程序

```bash
# JSON模式
./amazon_checker.exe

# SQLite3模式（如果编译了SQLite版本）
./amazon_checker_sqlite.exe
```

## 配置说明

### 数据库配置 (database)

- **path**: 数据库文件路径
- **type**: 数据库类型
  - `"json"`: 使用JSON文件存储（推荐，无需CGO）
  - `"sqlite"`: 使用SQLite3数据库（需要GCC编译器）

### 工作器配置 (worker)

- **num_threads**: 并发线程数，建议根据您的网络和系统性能调整
- **accounts_per_batch**: 每批处理的账号数量
- **start_index**: 开始处理的账号索引，用于断点续传
- **proxy_per_batch**: 代理池大小，影响代理轮换频率

### 服务器配置 (server)

- **code_server**: 验证码识别服务器地址
- **proxy_api**: 代理获取API地址

### URL配置 (urls)

Amazon相关的URL配置，通常不需要修改。

## 编译模式

### JSON模式 (推荐)
- 优点：纯Go实现，编译简单，无需CGO
- 缺点：性能略低于SQLite3
- 适用：大多数使用场景

### SQLite3模式
- 优点：性能更好，支持复杂查询
- 缺点：需要CGO和GCC编译器
- 适用：高性能需求场景

## 从环境变量迁移

如果您之前使用环境变量配置，现在可以将这些设置移动到 `config.yaml` 文件中：

| 环境变量 | YAML配置路径 |
|---------|-------------|
| `DB_PATH` | `database.path` |
| `NUM_THREADS` | `worker.num_threads` |
| `ACCOUNTS_PER_BATCH` | `worker.accounts_per_batch` |
| `START_INDEX` | `worker.start_index` |
| `PROXY_PER_BATCH` | `worker.proxy_per_batch` |

## 故障排除

### 编译问题
1. 如果SQLite3编译失败，使用JSON模式
2. 确保Go版本 >= 1.22
3. 网络问题可能影响依赖下载

### 运行问题
1. 检查 `config.yaml` 文件格式是否正确
2. 确保数据库文件路径存在
3. 验证账号数据格式

## 更新日志

- ✅ 替换环境变量为YAML配置文件
- ✅ 支持SQLite3和JSON双模式
- ✅ 自动生成默认配置文件
- ✅ 配置验证和错误提示
- ✅ 更新构建脚本支持YAML配置

## 技术细节

- 使用 `gopkg.in/yaml.v3` 解析YAML配置
- 支持Go build tags进行条件编译
- 配置验证确保参数有效性
- 向后兼容原有数据格式
