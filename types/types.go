package types

// StatusCode 登录状态枚举
type StatusCode string

const (
	IndexNeedCaptcha           StatusCode = "首页需验证码-无法验证通过"
	NeedCaptcha               StatusCode = "需验证码-无法验证通过"
	FoundErrLoginElement      StatusCode = "出现错误的登录元素"
	NotFoundCaptchaImg        StatusCode = "无法找到验证码图片"
	CaptchaErr                StatusCode = "验证码识别错误"
	LoginFailAccountPhoneNotFound StatusCode = "登录失败-不存在该账号（电话）"
	LoginFailAccountEmailNotFound StatusCode = "登录失败-不存在该账号（邮箱）"
	LoginFailNeedResetPassword    StatusCode = "登录失败-需重置密码"
	LoginFailNotFoundSite         StatusCode = "登录失败-无法找到站点"
	LoginFailPasswordError        StatusCode = "登录失败-密码错误"
	LoginFailNeedOTP             StatusCode = "登录失败-需要OTP验证"
	LoginFailNeedCode            StatusCode = "登录失败-需要验证码验证"
	LoginFailUnknownCause        StatusCode = "登录失败-未知原因"
	LoginSuccess                 StatusCode = "登录成功"
)

// RetryStatusList 需要重试的状态列表
var RetryStatusList = []StatusCode{
	IndexNeedCaptcha,
	NeedCaptcha,
	FoundErrLoginElement,
	NotFoundCaptchaImg,
	CaptchaErr,
	LoginFailNotFoundSite,
}

// NeedRecordList 需要记录的状态列表
var NeedRecordList = []StatusCode{
	LoginFailNotFoundSite,
	LoginFailUnknownCause,
}

// AccountInfo 账号信息（用于worker）
type AccountInfo struct {
	ID       uint   `json:"id"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

// Result 登录结果
type Result struct {
	Status StatusCode  `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}

// ResultData 结果数据
type ResultData struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Cookie   string `json:"cookie"`
	Type     string `json:"type"`
	HTML     string `json:"html,omitempty"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	HTTP  string `json:"http"`
	HTTPS string `json:"https"`
}

// WorkerConfig 工作器配置
type WorkerConfig struct {
	NumThreads       int `yaml:"num_threads"`
	AccountsPerBatch int `yaml:"accounts_per_batch"`
	StartIndex       int `yaml:"start_index"`
	ProxyPerBatch    int `yaml:"proxy_per_batch"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path string `yaml:"path"`
	Type string `yaml:"type"` // "sqlite" 或 "json"
}

// ServerConfig 服务器配置
type ServerConfig struct {
	CodeServer string `yaml:"code_server"`
	ProxyAPI   string `yaml:"proxy_api"`
}

// URLConfig URL配置
type URLConfig struct {
	Amazon    string `yaml:"amazon"`
	Login     string `yaml:"login"`
	Captcha   string `yaml:"captcha"`
	CVFVerify string `yaml:"cvf_verify"`
}

// AppConfig 应用程序配置
type AppConfig struct {
	Database DatabaseConfig `yaml:"database"`
	Worker   WorkerConfig   `yaml:"worker"`
	Server   ServerConfig   `yaml:"server"`
	URLs     URLConfig      `yaml:"urls"`
}

// TouchData 触摸数据
type TouchData struct {
	Na  int `json:"na_"`
	Ul  int `json:"ul_"`
	UlE int `json:"_ul"`
	Rd  int `json:"rd_"`
	RdE int `json:"_rd"`
	Fe  int `json:"fe_"`
	Lk  int `json:"lk_"`
	LkE int `json:"_lk"`
	Co  int `json:"co_"`
	CoE int `json:"_co"`
	Sc  int `json:"sc_"`
	Rq  int `json:"rq_"`
	Rs  int `json:"rs_"`
	RsE int `json:"_rs"`
	Dl  int `json:"dl_"`
	Di  int `json:"di_"`
	De  int `json:"de_"`
	DeE int `json:"_de"`
	Dc  int `json:"_dc"`
	Ld  int `json:"ld_"`
	LdE int `json:"_ld"`
}

// MetadataRequest metadata请求参数
type MetadataRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	Type       string `json:"type"`
	TimeZone   string `json:"timeZone"`
	LoginHref  string `json:"login_href"`
	UserAgent  string `json:"userAgent"`
	Document   string `json:"document"`
	Time       int64  `json:"time"`
}

// CaptchaResponse 验证码识别响应
type CaptchaResponse struct {
	Error int    `json:"error"`
	Code  string `json:"code"`
}

// ProxyAPIResponse 代理API响应
type ProxyAPIResponse struct {
	Code      int         `json:"code"`
	Success   bool        `json:"success"`
	Message   string      `json:"msg"`
	RequestIP string      `json:"request_ip"`
	Data      interface{} `json:"data"`
}

// HTTPHeaders HTTP请求头
type HTTPHeaders map[string]string

// DefaultGetHeaders 默认GET请求头
func DefaultGetHeaders() HTTPHeaders {
	return HTTPHeaders{
		"User-Agent":                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
		"Pragma":                    "no-cache",
		"Accept-Language":           "en-US,en;q=0.9",
		"Referer":                   "https://www.amazon.com",
		"Origin":                    "https://www.amazon.com",
		"Accept-Encoding":           "gzip, deflate, br",
		"Upgrade-Insecure-Requests": "1",
		"Connection":                "keep-alive",
	}
}

// DefaultPostHeaders 默认POST请求头
func DefaultPostHeaders() HTTPHeaders {
	return HTTPHeaders{
		"User-Agent":                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Content-Type":              "application/x-www-form-urlencoded",
		"Accept-Language":           "en-US,en;q=0.9",
		"Host":                      "www.amazon.com",
		"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
		"Cache-Control":             "no-cache",
		"Pragma":                    "no-cache",
		"Origin":                    "https://www.amazon.com",
		"Accept-Encoding":           "gzip, deflate, br",
		"Upgrade-Insecure-Requests": "1",
		"Connection":                "keep-alive",
		"Referer":                   "https://www.amazon.com",
	}
}

// 注意：常量已移动到配置文件中，通过AppConfig访问
