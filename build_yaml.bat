@echo off
echo Amazon Account Checker - YAML Config Build Script
echo ==================================================

REM Check for GCC compiler
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: GCC compiler not found. Building JSON mode...
    echo.
    set CGO_ENABLED=0
    go build -tags "json_mode" -o amazon_checker.exe
    if %errorlevel% equ 0 (
        echo Success: JSON version built - amazon_checker.exe
        echo Config: config.yaml (database.type: "json")
        echo.
        echo Tip: For SQLite3 support, install GCC compiler:
        echo   - TDM-GCC: https://jmeubank.github.io/tdm-gcc/
        echo   - MinGW-w64: https://www.mingw-w64.org/
        echo   Then set database.type: "sqlite" in config.yaml
    ) else (
        echo Error: JSON version build failed
        exit /b 1
    )
) else (
    echo GCC found. Building SQLite3 version...
    echo.
    set CGO_ENABLED=1
    go build -o amazon_checker_sqlite.exe
    if %errorlevel% equ 0 (
        echo Success: SQLite3 version built - amazon_checker_sqlite.exe
        echo Config: config.yaml (database.type: "sqlite")
    ) else (
        echo Error: SQLite3 version build failed
        exit /b 1
    )
)

echo.
echo Configuration Guide:
echo   - Edit config.yaml to modify settings
echo   - Database types: sqlite or json
echo   - Adjust threads, proxy settings in config file
echo.
echo Build completed!
pause
