package worker

import (
	"fmt"
	"log"
	"math/rand"
	"regexp"
	"strings"
	"sync"
	"time"

	"Ams/captcha"
	"Ams/database"
	"Ams/metadata"
	"Ams/proxy"
	"Ams/types"
	"github.com/imroc/req/v3"
)

// MultiThreadWorker 多线程工作器
type MultiThreadWorker struct {
	config        *types.WorkerConfig
	appConfig     *types.AppConfig
	proxyManager  *proxy.ProxyManager
	captchaSolver *captcha.CaptchaSolver
	metadataGen   *metadata.MetadataGenerator
	dbManager     *database.DatabaseManager
	accountQueue  chan types.AccountInfo
	mutex         sync.Mutex
	currentIndex  int
	accountList   []types.AccountInfo
	userAgents    []string
}

// NewMultiThreadWorker 创建多线程工作器
func NewMultiThreadWorker(config *types.WorkerConfig, appConfig *types.AppConfig, dbManager *database.DatabaseManager) (*MultiThreadWorker, error) {
	// 初始化代理管理器
	proxyManager := proxy.NewProxyManager(config.ProxyPerBatch, appConfig.Server.ProxyAPI)
	if err := proxyManager.InitializeProxies(); err != nil {
		return nil, fmt.Errorf("failed to initialize proxies: %v", err)
	}

	// 获取账号列表
	dbAccounts, err := dbManager.GetAllAccounts()
	if err != nil {
		return nil, fmt.Errorf("failed to get accounts: %v", err)
	}

	// 转换为AccountInfo类型
	accounts := make([]types.AccountInfo, len(dbAccounts))
	for i, acc := range dbAccounts {
		accounts[i] = types.AccountInfo{
			ID:       acc.ID,
			Email:    acc.Email,
			Password: acc.Password,
		}
	}

	// 初始化User-Agent列表
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
	}

	worker := &MultiThreadWorker{
		config:        config,
		appConfig:     appConfig,
		proxyManager:  proxyManager,
		captchaSolver: captcha.NewCaptchaSolver(appConfig.Server.CodeServer),
		metadataGen:   metadata.NewMetadataGenerator(),
		dbManager:     dbManager,
		accountQueue:  make(chan types.AccountInfo, config.AccountsPerBatch),
		currentIndex:  config.StartIndex,
		accountList:   accounts,
		userAgents:    userAgents,
	}

	return worker, nil
}

// generateResult 生成结果
func (w *MultiThreadWorker) generateResult(status types.StatusCode, data interface{}) *types.Result {
	return &types.Result{
		Status: status,
		Data:   data,
	}
}

// getRandomUserAgent 获取随机User-Agent
func (w *MultiThreadWorker) getRandomUserAgent() string {
	return w.userAgents[rand.Intn(len(w.userAgents))]
}

// getCookieHeader 获取Cookie头
func (w *MultiThreadWorker) getCookieHeader(client *req.Client) string {
	cookies, _ := client.GetCookies(w.appConfig.URLs.Amazon)
	var cookieStrs []string
	for _, cookie := range cookies {
		cookieStrs = append(cookieStrs, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
	}
	return strings.Join(cookieStrs, "; ")
}

// extractLoginFormData 提取登录表单数据
func (w *MultiThreadWorker) extractLoginFormData(htmlContent string) (map[string]string, error) {
	formData := make(map[string]string)

	// 使用正则表达式查找signIn表单中的隐藏字段
	formPattern := regexp.MustCompile(`<form[^>]*name=["']signIn["'][^>]*>(.*?)</form>`)
	formMatch := formPattern.FindStringSubmatch(htmlContent)
	if len(formMatch) < 2 {
		return formData, nil
	}

	formContent := formMatch[1]

	// 查找隐藏输入字段
	inputPattern := regexp.MustCompile(`<input[^>]*type=["']hidden["'][^>]*>`)
	inputs := inputPattern.FindAllString(formContent, -1)

	for _, input := range inputs {
		namePattern := regexp.MustCompile(`name=["']([^"']+)["']`)
		valuePattern := regexp.MustCompile(`value=["']([^"']*)["']`)

		nameMatch := namePattern.FindStringSubmatch(input)
		valueMatch := valuePattern.FindStringSubmatch(input)

		if len(nameMatch) >= 2 {
			name := nameMatch[1]
			value := ""
			if len(valueMatch) >= 2 {
				value = valueMatch[1]
			}
			formData[name] = value
		}
	}

	return formData, nil
}

// extractCaptchaFormData 提取验证码表单数据
func (w *MultiThreadWorker) extractCaptchaFormData(htmlContent string) (map[string]string, error) {
	formData := make(map[string]string)

	// 使用正则表达式查找验证码表单中的隐藏字段
	formPattern := regexp.MustCompile(`<form[^>]*class=["'][^"']*cvf-widget-form-captcha[^"']*["'][^>]*>(.*?)</form>`)
	formMatch := formPattern.FindStringSubmatch(htmlContent)
	if len(formMatch) < 2 {
		return formData, nil
	}

	formContent := formMatch[1]

	// 查找隐藏输入字段
	inputPattern := regexp.MustCompile(`<input[^>]*type=["']hidden["'][^>]*>`)
	inputs := inputPattern.FindAllString(formContent, -1)

	for _, input := range inputs {
		namePattern := regexp.MustCompile(`name=["']([^"']+)["']`)
		valuePattern := regexp.MustCompile(`value=["']([^"']*)["']`)

		nameMatch := namePattern.FindStringSubmatch(input)
		valueMatch := valuePattern.FindStringSubmatch(input)

		if len(nameMatch) >= 2 {
			name := nameMatch[1]
			value := ""
			if len(valueMatch) >= 2 {
				value = valueMatch[1]
			}
			formData[name] = value
		}
	}

	return formData, nil
}

// extractIndexCaptchaFormData 提取首页验证码表单数据
func (w *MultiThreadWorker) extractIndexCaptchaFormData(htmlContent string) (map[string]string, error) {
	formData := make(map[string]string)

	// 使用正则表达式查找首页验证码表单中的隐藏字段
	formPattern := regexp.MustCompile(`<form[^>]*action=["'][^"']*\/errors\/validateCaptcha[^"']*["'][^>]*>(.*?)</form>`)
	formMatch := formPattern.FindStringSubmatch(htmlContent)
	if len(formMatch) < 2 {
		return formData, nil
	}

	formContent := formMatch[1]

	// 查找隐藏输入字段
	inputPattern := regexp.MustCompile(`<input[^>]*type=["']hidden["'][^>]*>`)
	inputs := inputPattern.FindAllString(formContent, -1)

	for _, input := range inputs {
		namePattern := regexp.MustCompile(`name=["']([^"']+)["']`)
		valuePattern := regexp.MustCompile(`value=["']([^"']*)["']`)

		nameMatch := namePattern.FindStringSubmatch(input)
		valueMatch := valuePattern.FindStringSubmatch(input)

		if len(nameMatch) >= 2 {
			name := nameMatch[1]
			value := ""
			if len(valueMatch) >= 2 {
				value = valueMatch[1]
			}
			formData[name] = value
		}
	}

	return formData, nil
}

// findCaptchaImage 查找验证码图片URL
func (w *MultiThreadWorker) findCaptchaImage(htmlContent string) (string, error) {
	// 尝试多种正则表达式模式查找验证码图片
	patterns := []string{
		`<img[^>]*alt=["']captcha["'][^>]*src=["']([^"']+)["']`,
		`<img[^>]*src=["']([^"']+)["'][^>]*alt=["']captcha["']`,
		`<div[^>]*class=["'][^"']*a-row[^"']*a-text-center[^"']*["'][^>]*>.*?<img[^>]*src=["']([^"']+)["']`,
		`<img[^>]*src=["']([^"']*[Cc]aptcha[^"']*)["']`,
		`<img[^>]*src=["']([^"']*images-na\.ssl[^"']*)["']`,
		`<img[^>]*src=["']([^"']*s3\.amazonaws[^"']*)["']`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(htmlContent)
		if len(matches) >= 2 && matches[1] != "" {
			return matches[1], nil
		}
	}

	return "", fmt.Errorf("captcha image not found")
}

// checkLoginElements 检查登录元素
func (w *MultiThreadWorker) checkLoginElements(htmlContent string) error {
	// 检查是否有错误的登录元素
	errorPattern := regexp.MustCompile(`<div[^>]*class=["'][^"']*nav-bb-right[^"']*["'][^>]*>.*?<a[^>]*href=["'][^"']*css\/homepage[^"']*["']`)
	if errorPattern.MatchString(htmlContent) {
		return fmt.Errorf("found error login element")
	}

	// 检查账户链接是否存在
	accountLinkPattern := regexp.MustCompile(`<a[^>]*id=["']nav-link-accountList["']`)
	if !accountLinkPattern.MatchString(htmlContent) {
		return fmt.Errorf("account link not found")
	}

	return nil
}

// getLoginURL 获取登录URL
func (w *MultiThreadWorker) getLoginURL(htmlContent string) (string, error) {
	// 使用正则表达式查找登录URL
	pattern := regexp.MustCompile(`<a[^>]*id=["']nav-link-accountList["'][^>]*href=["']([^"']+)["']`)
	matches := pattern.FindStringSubmatch(htmlContent)
	if len(matches) >= 2 && matches[1] != "" {
		return matches[1], nil
	}

	return "", fmt.Errorf("login URL not found")
}

// checkLoginSuccess 检查登录是否成功
func (w *MultiThreadWorker) checkLoginSuccess(htmlContent string) bool {
	// 检查账户链接
	pattern := regexp.MustCompile(`<a[^>]*id=["']nav-link-accountList["'][^>]*href=["']([^"']+)["']`)
	matches := pattern.FindStringSubmatch(htmlContent)
	if len(matches) >= 2 && strings.Contains(matches[1], "css/homepage") {
		return true
	}

	return false
}

// checkNeedPhoneSkip 检查是否需要跳过手机验证
func (w *MultiThreadWorker) checkNeedPhoneSkip(htmlContent string) (string, bool) {
	// 使用正则表达式查找跳过手机验证的链接
	pattern := regexp.MustCompile(`<a[^>]*id=["']ap-db-fixup-phone-skip-link["'][^>]*href=["']([^"']+)["']`)
	matches := pattern.FindStringSubmatch(htmlContent)
	if len(matches) >= 2 && matches[1] != "" {
		skipURL := matches[1]
		// 确保URL是完整的
		if !strings.HasPrefix(skipURL, "https://") {
			skipURL = "https://www.amazon.com" + skipURL
		}
		return skipURL, true
	}

	return "", false
}

// analyzeLoginResponse 分析登录响应
func (w *MultiThreadWorker) analyzeLoginResponse(htmlContent string) types.StatusCode {
	// 检查各种登录失败情况
	if strings.Contains(htmlContent, "We cannot find an account with that email address") {
		return types.LoginFailAccountEmailNotFound
	}

	if strings.Contains(htmlContent, "We cannot find an account with that mobile number") {
		return types.LoginFailAccountPhoneNotFound
	}

	if strings.Contains(htmlContent, "Password reset required") {
		return types.LoginFailNeedResetPassword
	}

	if strings.Contains(htmlContent, "The Web address you entered is not a functioning page on our site") {
		return types.LoginFailNotFoundSite
	}

	if strings.Contains(htmlContent, "Your password is incorrect") {
		return types.LoginFailPasswordError
	}

	if strings.Contains(htmlContent, "Two-Step Verification") {
		return types.LoginFailNeedOTP
	}

	if strings.Contains(htmlContent, "Enter verification code") {
		return types.LoginFailNeedCode
	}

	return types.LoginFailUnknownCause
}

// work 执行单个账号的登录工作
func (w *MultiThreadWorker) work(account types.AccountInfo, proxyURL string) *types.Result {
	username := account.Email
	password := account.Password

	resultData := &types.ResultData{
		Username: username,
		Password: password,
		Cookie:   "",
		Type:     "",
	}

	// 获取随机User-Agent
	userAgent := w.getRandomUserAgent()

	// 创建HTTP客户端
	client := req.C().
		SetTimeout(30 * time.Second).
		SetProxyURL(proxyURL).
		ImpersonateChrome()

	// 设置请求头
	getHeaders := types.DefaultGetHeaders()
	getHeaders["User-Agent"] = userAgent

	postHeaders := types.DefaultPostHeaders()
	postHeaders["User-Agent"] = userAgent

	// Step 1: 访问首页
	step1Resp, err := client.R().
		SetHeaders(getHeaders).
		Get(w.appConfig.URLs.Amazon)

	if err != nil {
		return w.generateResult(types.LoginFailNotFoundSite, resultData)
	}

	step1HTML := step1Resp.String()

	// 检查首页是否需要验证码
	if strings.Contains(step1HTML, "Enter the characters you see below") {
		// 处理首页验证码
		formData, err := w.extractIndexCaptchaFormData(step1HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		captchaURL, err := w.findCaptchaImage(step1HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		code, err := w.captchaSolver.SolveCaptcha(captchaURL, client, userAgent)
		if err != nil {
			return w.generateResult(types.CaptchaErr, resultData)
		}

		formData["field-keywords"] = code
		postHeaders["Referer"] = w.appConfig.URLs.Amazon

		step1_1Resp, err := client.R().
			SetHeaders(postHeaders).
			SetFormData(formData).
			Post(w.appConfig.URLs.Captcha)

		if err != nil {
			return w.generateResult(types.LoginFailNotFoundSite, resultData)
		}

		step1HTML = step1_1Resp.String()

		w.mutex.Lock()
		fmt.Println("首页验证码验证成功！！！")
		w.mutex.Unlock()
	}

	// 检查验证后是否还需要验证码
	if strings.Contains(step1HTML, "Enter the characters you see below") {
		return w.generateResult(types.IndexNeedCaptcha, resultData)
	}

	// 检查登录元素
	if err := w.checkLoginElements(step1HTML); err != nil {
		return w.generateResult(types.FoundErrLoginElement, resultData)
	}

	// 获取登录URL
	loginURL, err := w.getLoginURL(step1HTML)
	if err != nil {
		return w.generateResult(types.FoundErrLoginElement, resultData)
	}

	time.Sleep(1 * time.Second)

	// Step 2: 访问登录页
	step2Resp, err := client.R().
		SetHeaders(getHeaders).
		Get(loginURL)

	if err != nil {
		return w.generateResult(types.LoginFailNotFoundSite, resultData)
	}

	step2HTML := step2Resp.String()

	// 提取登录表单数据
	loginFormData, err := w.extractLoginFormData(step2HTML)
	if err != nil {
		return w.generateResult(types.FoundErrLoginElement, resultData)
	}

	loginFormData["email"] = username
	loginFormData["password"] = password
	loginFormData["rememberMe"] = "true"
	loginFormData["metadata1"] = ""

	time.Sleep(1 * time.Second)

	// Step 3: 提交登录表单
	postHeaders["Referer"] = "https://www.amazon.com/ap/signin"

	step4Resp, err := client.R().
		SetHeaders(postHeaders).
		SetFormData(loginFormData).
		Post(w.appConfig.URLs.Login)

	if err != nil {
		return w.generateResult(types.LoginFailNotFoundSite, resultData)
	}

	step4HTML := step4Resp.String()

	time.Sleep(1 * time.Second)

	// 检查是否需要验证码
	if strings.Contains(step4HTML, "cvf-widget-form-captcha") {
		fmt.Println("!!!验证码")

		captchaURL, err := w.findCaptchaImage(step4HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		code, err := w.captchaSolver.SolveCaptcha(captchaURL, client, userAgent)
		if err != nil {
			return w.generateResult(types.CaptchaErr, resultData)
		}

		captchaFormData, err := w.extractCaptchaFormData(step4HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		captchaFormData["cvf_captcha_input"] = code

		// 生成metadata1
		metadataReq := &types.MetadataRequest{
			Username:  username,
			Password:  password,
			Type:      "signIn",
			TimeZone:  "-8",
			LoginHref: loginURL,
			UserAgent: userAgent,
			Document:  step2HTML,
			Time:      metadata.GetMillisecond(),
		}

		metadata1, err := w.metadataGen.GenerateMetadata1(metadataReq)
		if err != nil {
			log.Printf("Failed to generate metadata1: %v", err)
			metadata1 = ""
		}

		captchaFormData["metadata1"] = metadata1

		postHeaders["Referer"] = step4Resp.Request.URL.String()

		step4Resp, err = client.R().
			SetHeaders(postHeaders).
			SetFormData(captchaFormData).
			Post(w.appConfig.URLs.CVFVerify)

		if err != nil {
			return w.generateResult(types.LoginFailNotFoundSite, resultData)
		}

		step4HTML = step4Resp.String()
	}

	time.Sleep(1 * time.Second)

	// 检查提交后是否还需要验证码
	if strings.Contains(step4HTML, "Enter the characters you see below") ||
		strings.Contains(step4HTML, "Enter the letters and numbers above") {
		fmt.Println("!!!尾页验证码")

		formData, err := w.extractIndexCaptchaFormData(step4HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		captchaURL, err := w.findCaptchaImage(step4HTML)
		if err != nil {
			return w.generateResult(types.NotFoundCaptchaImg, resultData)
		}

		code, err := w.captchaSolver.SolveCaptcha(captchaURL, client, userAgent)
		if err != nil {
			return w.generateResult(types.CaptchaErr, resultData)
		}

		formData["field-keywords"] = code
		postHeaders["Referer"] = step4Resp.Request.URL.String()

		step4_1Resp, err := client.R().
			SetHeaders(postHeaders).
			SetFormData(formData).
			Post(w.appConfig.URLs.Captcha)

		if err != nil {
			return w.generateResult(types.LoginFailNotFoundSite, resultData)
		}

		step4HTML = step4_1Resp.String()
	}

	time.Sleep(1 * time.Second)

	// 最终检查是否还需要验证码
	if strings.Contains(step4HTML, "Enter the characters you see below") {
		return w.generateResult(types.NeedCaptcha, resultData)
	}

	// 检查是否需要跳过手机验证
	if skipURL, needSkip := w.checkNeedPhoneSkip(step4HTML); needSkip {
		fmt.Println("!!!需要跳过手机验证")

		getHeaders["Referer"] = step4Resp.Request.URL.String()
		_, err := client.R().
			SetHeaders(getHeaders).
			Get(skipURL)

		if err == nil {
			resultData.Cookie = w.getCookieHeader(client)
			resultData.Type = "skip_phone"
			return w.generateResult(types.LoginSuccess, resultData)
		}
	}

	// 分析登录响应
	status := w.analyzeLoginResponse(step4HTML)

	// 检查登录是否成功
	if w.checkLoginSuccess(step4HTML) {
		resultData.Cookie = w.getCookieHeader(client)
		return w.generateResult(types.LoginSuccess, resultData)
	}

	// 如果是未知原因失败，保存HTML用于调试
	if status == types.LoginFailUnknownCause {
		resultData.HTML = step4HTML
	}

	return w.generateResult(status, resultData)
}

// worker 工作线程
func (w *MultiThreadWorker) worker() {
	for {
		select {
		case account := <-w.accountQueue:
			recordNum := 0
			maxRetries := 10

			for recordNum <= maxRetries {
				w.mutex.Lock()
				fmt.Printf("开始验证账号：%s  当前次数：%d\n", account.Email, recordNum+1)
				w.mutex.Unlock()

				recordNum++

				// 更新账号状态为处理中
				if recordNum == 1 {
					w.dbManager.UpdateAccountStatus(account.ID, "processing")
				}

				// 获取代理
				proxyURL, err := w.proxyManager.GetWorkingProxy()
				if err != nil {
					w.mutex.Lock()
					fmt.Printf("获取代理失败: %v\n", err)
					w.mutex.Unlock()
					continue
				}

				// 执行登录工作
				result := w.work(account, proxyURL)

				w.mutex.Lock()
				fmt.Printf("【结果】：%s  %s\n", result.Status, account.Email)
				w.mutex.Unlock()

				// 写入结果数据
				if result.Data != nil {
					if resultData, ok := result.Data.(*types.ResultData); ok {
						_, err := w.dbManager.CreateResult(
							account.ID,
							resultData.Username,
							resultData.Password,
							resultData.Cookie,
							resultData.Type,
							string(result.Status),
							resultData.HTML,
						)
						if err != nil {
							w.mutex.Lock()
							fmt.Printf("写入结果失败: %v\n", err)
							w.mutex.Unlock()
						} else {
							w.mutex.Lock()
							fmt.Println("【写入了一个结果】")
							w.mutex.Unlock()
						}

						// 更新账号状态
						if result.Status == types.LoginSuccess {
							w.dbManager.UpdateAccountStatus(account.ID, "completed")
						}
					}
				}

				// 检查是否需要重试
				needRetry := false
				for _, retryStatus := range types.RetryStatusList {
					if result.Status == retryStatus {
						needRetry = true
						break
					}
				}

				if !needRetry {
					break
				}

				// 等待一段时间后重试
				time.Sleep(time.Duration(rand.Intn(3)+1) * time.Second)
			}

			// 如果重试次数超过限制，写入错误数据
			if recordNum > maxRetries {
				w.mutex.Lock()
				fmt.Println("写入一个错误数据")
				w.mutex.Unlock()

				_, err := w.dbManager.CreateErrorRecord(
					account.ID,
					account.Email,
					account.Password,
					"重试次数超过限制",
					recordNum,
				)
				if err != nil {
					w.mutex.Lock()
					fmt.Printf("写入错误数据失败: %v\n", err)
					w.mutex.Unlock()
				}

				// 更新账号状态为失败
				w.dbManager.UpdateAccountStatus(account.ID, "failed")
			}

		case <-time.After(5 * time.Second):
			// 队列为空，等待一段时间后重试
			w.mutex.Lock()
			fmt.Println("队列为空，等待一会再尝试...")
			w.mutex.Unlock()
			time.Sleep(1 * time.Second)
		}
	}
}

// Start 启动多线程工作器
func (w *MultiThreadWorker) Start() error {
	// 启动工作线程
	for i := 0; i < w.config.NumThreads; i++ {
		go w.worker()
	}

	fmt.Printf("启动了 %d 个工作线程\n", w.config.NumThreads)

	// 处理账号队列
	for w.currentIndex < len(w.accountList) {
		// 填充账户队列
		batchEnd := w.currentIndex + w.config.AccountsPerBatch
		if batchEnd > len(w.accountList) {
			batchEnd = len(w.accountList)
		}

		for i := w.currentIndex; i < batchEnd; i++ {
			select {
			case w.accountQueue <- w.accountList[i]:
				w.currentIndex++
			case <-time.After(10 * time.Second):
				fmt.Println("队列已满，等待处理...")
			}
		}

		// 等待当前批次处理完成
		for len(w.accountQueue) > 0 {
			time.Sleep(1 * time.Second)
		}

		// 输出进度
		w.mutex.Lock()
		fmt.Printf("当前进度： %d/%d\n", w.currentIndex, len(w.accountList))
		w.mutex.Unlock()

		// 补充代理池
		if err := w.proxyManager.RefillProxies(); err != nil {
			fmt.Printf("补充代理失败: %v\n", err)
		}
	}

	fmt.Println("所有账号处理完成")
	return nil
}

// GetProgress 获取处理进度
func (w *MultiThreadWorker) GetProgress() (int, int) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	return w.currentIndex, len(w.accountList)
}

// Stop 停止工作器
func (w *MultiThreadWorker) Stop() {
	close(w.accountQueue)
}
