# Amazon账号登录验证工具配置文件

# 数据库配置
database:
  # 数据库文件路径
  path: "db/amazon.db"
  # 数据库类型: "sqlite" 或 "json"
  # 注意: sqlite需要CGO支持，如果编译时遇到问题请使用json
  type: "json"

# 工作器配置
worker:
  # 并发线程数
  num_threads: 1
  # 每批处理的账号数量
  accounts_per_batch: 1
  # 开始处理的账号索引（从0开始）
  start_index: 0
  # 代理池大小
  proxy_per_batch: 100

# 服务器配置
server:
  # 验证码识别服务器地址
  code_server: "http://*************:38787"
  # 代理API地址
  proxy_api: "https://acq.iemoapi.com/getProxyIp?lb=1&return_type=txt&protocol=http&num="

# URL配置
urls:
  # Amazon主页URL
  amazon: "https://www.amazon.com/"
  # 登录页面URL
  login: "https://www.amazon.com/ap/signin"
  # 验证码验证URL
  captcha: "https://www.amazon.com/errors/validateCaptcha"
  # CVF验证URL
  cvf_verify: "https://www.amazon.com/ap/cvf/verify"
