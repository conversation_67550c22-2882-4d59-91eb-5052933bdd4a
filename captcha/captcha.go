package captcha

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"Ams/types"
	"github.com/imroc/req/v3"
)

// CaptchaSolver 验证码解决器
type CaptchaSolver struct {
	serverURL string
	client    *req.Client
}

// NewCaptchaSolver 创建验证码解决器
func NewCaptchaSolver(serverURL string) *CaptchaSolver {
	client := req.C().
		SetTimeout(30 * time.Second)

	return &CaptchaSolver{
		serverURL: serverURL,
		client:    client,
	}
}

// SolveCaptcha 解决验证码
func (cs *CaptchaSolver) SolveCaptcha(captchaImgURL string, reqClient *req.Client, userAgent string) (string, error) {
	// 根据URL选择不同的解决方案
	var solveURL string
	if strings.Contains(captchaImgURL, "images-na.ssl") {
		solveURL = cs.serverURL + "/solve_resources64_easy"
	} else if strings.Contains(captchaImgURL, "s3.amazonaws.com") {
		solveURL = cs.serverURL + "/solve_resources64"
	} else {
		solveURL = cs.serverURL + "/solve_resources64"
	}

	// 下载验证码图片
	headers := types.DefaultGetHeaders()
	if userAgent != "" {
		headers["User-Agent"] = userAgent
	}

	resp, err := reqClient.R().
		SetHeaders(headers).
		Get(captchaImgURL)

	if err != nil {
		return "", fmt.Errorf("failed to download captcha image: %v", err)
	}

	// 将图片转换为base64
	imageBase64 := base64.StdEncoding.EncodeToString(resp.Bytes())

	// 发送到验证码识别服务
	data := map[string]string{
		"resources": imageBase64,
	}

	solveResp, err := cs.client.R().
		SetFormData(data).
		Post(solveURL)

	if err != nil {
		return "", fmt.Errorf("failed to solve captcha: %v", err)
	}

	// 解析响应
	var result types.CaptchaResponse
	if err := json.Unmarshal(solveResp.Bytes(), &result); err != nil {
		return "", fmt.Errorf("failed to parse captcha response: %v", err)
	}

	if result.Error != 0 {
		return "", fmt.Errorf("captcha solving failed with error code: %d", result.Error)
	}

	if result.Code == "" {
		return "", fmt.Errorf("empty captcha code returned")
	}

	return result.Code, nil
}

// SolveCaptchaWithRetry 带重试的验证码解决
func (cs *CaptchaSolver) SolveCaptchaWithRetry(captchaImgURL string, reqClient *req.Client, userAgent string, maxRetries int) (string, error) {
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		code, err := cs.SolveCaptcha(captchaImgURL, reqClient, userAgent)
		if err == nil && code != "" {
			return code, nil
		}

		lastErr = err

		// 等待一段时间后重试
		if i < maxRetries-1 {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	return "", fmt.Errorf("failed to solve captcha after %d retries: %v", maxRetries, lastErr)
}

// ValidateCaptchaImage 验证验证码图片URL是否有效
func (cs *CaptchaSolver) ValidateCaptchaImage(captchaImgURL string) bool {
	if captchaImgURL == "" {
		return false
	}

	// 检查URL格式
	if !strings.HasPrefix(captchaImgURL, "http://") && !strings.HasPrefix(captchaImgURL, "https://") {
		return false
	}

	// 检查是否是图片URL
	lowerURL := strings.ToLower(captchaImgURL)
	return strings.Contains(lowerURL, "captcha") ||
		strings.Contains(lowerURL, "image") ||
		strings.Contains(lowerURL, ".jpg") ||
		strings.Contains(lowerURL, ".png") ||
		strings.Contains(lowerURL, ".gif")
}

// GetCaptchaType 获取验证码类型
func (cs *CaptchaSolver) GetCaptchaType(captchaImgURL string) string {
	if strings.Contains(captchaImgURL, "images-na.ssl") {
		return "easy"
	} else if strings.Contains(captchaImgURL, "s3.amazonaws.com") {
		return "standard"
	}
	return "standard"
}

// TestCaptchaService 测试验证码服务是否可用
func (cs *CaptchaSolver) TestCaptchaService() error {
	testURL := cs.serverURL + "/health"

	client := req.C().SetTimeout(10 * time.Second)
	resp, err := client.R().Get(testURL)

	if err != nil {
		return fmt.Errorf("captcha service unavailable: %v", err)
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("captcha service returned status code: %d", resp.StatusCode)
	}

	return nil
}
