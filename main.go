package main

import (
	"Ams/config"
	"Ams/database"
	"Ams/worker"
	"fmt"
	"log"
)

func main() {
	fmt.Println("Amazon账号登录验证工具 - Go版本")
	fmt.Println("=====================================")

	// 加载配置文件
	configPath := "config.yaml"
	appConfig, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("📋 配置信息:\n")
	fmt.Printf("  数据库类型: %s\n", appConfig.Database.Type)
	fmt.Printf("  数据库路径: %s\n", appConfig.Database.Path)
	fmt.Printf("  线程数: %d\n", appConfig.Worker.NumThreads)
	fmt.Printf("  每批账号数: %d\n", appConfig.Worker.AccountsPerBatch)
	fmt.Printf("  起始索引: %d\n", appConfig.Worker.StartIndex)
	fmt.Printf("  代理池大小: %d\n", appConfig.Worker.ProxyPerBatch)
	fmt.Println("=====================================")

	// 初始化数据库管理器
	dbManager, err := database.NewDatabaseManager(appConfig.Database.Path)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer dbManager.Close()

	// 获取账号总数
	accountCount, err := dbManager.CountAccounts()
	if err != nil {
		log.Fatalf("获取账号数量失败: %v", err)
	}

	fmt.Printf("数据库中共有 %d 个账号\n", accountCount)

	if accountCount == 0 {
		fmt.Println("数据库中没有账号，请先运行数据迁移脚本:")
		fmt.Println("python migrate_data.py db/amazon.db db/db_account.json")
		return
	}

	// 显示统计信息
	stats, err := dbManager.GetStatistics()
	if err != nil {
		log.Printf("获取统计信息失败: %v", err)
	} else {
		fmt.Println("\n📊 当前数据库状态:")
		fmt.Printf("  待处理: %d\n", stats["pending_accounts"])
		fmt.Printf("  处理中: %d\n", stats["processing_accounts"])
		fmt.Printf("  已完成: %d\n", stats["completed_accounts"])
		fmt.Printf("  成功结果: %d\n", stats["success_results"])
		fmt.Printf("  错误记录: %d\n", stats["error_records"])
	}

	// 验证起始索引
	if int64(appConfig.Worker.StartIndex) >= accountCount {
		log.Fatalf("起始索引 %d 超出账号总数 %d", appConfig.Worker.StartIndex, accountCount)
	}

	// 创建多线程工作器
	fmt.Println("正在初始化工作器...")
	multiWorker, err := worker.NewMultiThreadWorker(&appConfig.Worker, appConfig, dbManager)
	if err != nil {
		log.Fatalf("创建工作器失败: %v", err)
	}

	fmt.Println("工作器初始化完成，开始处理账号...")

	// 启动工作器
	if err := multiWorker.Start(); err != nil {
		log.Fatalf("启动工作器失败: %v", err)
	}

	fmt.Println("所有任务已完成！")
}


