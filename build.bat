@echo off
echo Amazon账号验证工具 - 构建脚本 (YAML配置版)
echo ===============================================

REM 检查是否有GCC编译器
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠ 未检测到GCC编译器，使用JSON模式
    echo 正在编译JSON版本...
    set CGO_ENABLED=0
    go build -tags "json_mode" -o amazon_checker.exe
    if %errorlevel% equ 0 (
        echo ✓ JSON版本编译成功: amazon_checker.exe
        echo   配置文件: config.yaml (database.type: "json")
        echo.
        echo 💡 提示: 如需SQLite3支持，请安装GCC编译器:
        echo    - TDM-GCC: https://jmeubank.github.io/tdm-gcc/
        echo    - MinGW-w64: https://www.mingw-w64.org/
        echo    然后在config.yaml中设置 database.type: "sqlite"
    ) else (
        echo ✗ JSON版本编译失败
        exit /b 1
    )
) else (
    echo ✓ 检测到GCC编译器，使用SQLite3模式
    echo 正在编译SQLite3版本...
    set CGO_ENABLED=1
    go build -o amazon_checker_sqlite.exe
    if %errorlevel% equ 0 (
        echo ✓ SQLite3版本编译成功: amazon_checker_sqlite.exe
        echo   配置文件: config.yaml (database.type: "sqlite")
    ) else (
        echo ✗ SQLite3版本编译失败
        exit /b 1
    )
)

echo.
echo 📋 配置说明:
echo   - 编辑 config.yaml 文件来修改配置
echo   - 数据库类型: sqlite 或 json
echo   - 线程数、代理设置等都可在配置文件中调整
echo.
echo 构建完成！
pause
