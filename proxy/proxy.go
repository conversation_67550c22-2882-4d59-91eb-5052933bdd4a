package proxy

import (
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"Ams/types"
	"github.com/imroc/req/v3"
)

// ProxyManager 代理管理器
type ProxyManager struct {
	proxyQueue chan *types.ProxyConfig
	mutex      sync.Mutex
	maxProxies int
	proxyAPI   string
}

// NewProxyManager 创建代理管理器
func NewProxyManager(maxProxies int, proxyAPI string) *ProxyManager {
	return &ProxyManager{
		proxyQueue: make(chan *types.ProxyConfig, maxProxies),
		maxProxies: maxProxies,
		proxyAPI:   proxyAPI,
	}
}

// FetchProxies 获取代理列表
func (pm *ProxyManager) FetchProxies(count int) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	fmt.Printf("Fetching %d proxies...\n", count)

	client := req.C().
		SetTimeout(30 * time.Second)

	proxyURL := fmt.Sprintf("%s%d", pm.proxyAPI, count)

	resp, err := client.R().Get(proxyURL)
	if err != nil {
		return fmt.Errorf("failed to fetch proxies: %v", err)
	}

	proxyText := resp.String()
	lines := strings.Split(proxyText, "\r\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		proxy := &types.ProxyConfig{
			HTTP:  line,
			HTTPS: line,
		}

		// 非阻塞发送到队列
		select {
		case pm.proxyQueue <- proxy:
		default:
			// 队列已满，跳过这个代理
		}
	}

	fmt.Printf("Successfully fetched %d proxies\n", len(lines))
	return nil
}

// GetProxy 获取一个代理
func (pm *ProxyManager) GetProxy() (*types.ProxyConfig, error) {
	// 如果队列为空，尝试获取新的代理
	if len(pm.proxyQueue) == 0 {
		if err := pm.FetchProxies(pm.maxProxies); err != nil {
			return nil, fmt.Errorf("failed to fetch new proxies: %v", err)
		}
	}

	select {
	case proxy := <-pm.proxyQueue:
		return proxy, nil
	case <-time.After(5 * time.Second):
		return nil, fmt.Errorf("timeout waiting for proxy")
	}
}

// GetProxyURL 获取代理URL用于req客户端
func (pm *ProxyManager) GetProxyURL() (string, error) {
	proxy, err := pm.GetProxy()
	if err != nil {
		return "", err
	}

	// 验证代理格式
	if !strings.HasPrefix(proxy.HTTP, "http://") {
		proxy.HTTP = "http://" + proxy.HTTP
	}

	// 验证URL格式
	_, err = url.Parse(proxy.HTTP)
	if err != nil {
		return "", fmt.Errorf("invalid proxy URL: %v", err)
	}

	return proxy.HTTP, nil
}

// QueueSize 获取队列大小
func (pm *ProxyManager) QueueSize() int {
	return len(pm.proxyQueue)
}

// InitializeProxies 初始化代理池
func (pm *ProxyManager) InitializeProxies() error {
	return pm.FetchProxies(pm.maxProxies)
}

// TestProxy 测试代理是否可用
func (pm *ProxyManager) TestProxy(proxyURL string) bool {
	client := req.C().
		SetTimeout(10 * time.Second).
		SetProxyURL(proxyURL)

	resp, err := client.R().Get("http://httpbin.org/ip")
	if err != nil {
		return false
	}

	return resp.StatusCode == 200
}

// GetWorkingProxy 获取一个可用的代理
func (pm *ProxyManager) GetWorkingProxy() (string, error) {
	maxRetries := 5

	for i := 0; i < maxRetries; i++ {
		proxyURL, err := pm.GetProxyURL()
		if err != nil {
			continue
		}

		if pm.TestProxy(proxyURL) {
			return proxyURL, nil
		}
	}

	return "", fmt.Errorf("no working proxy found after %d retries", maxRetries)
}

// RefillProxies 补充代理池
func (pm *ProxyManager) RefillProxies() error {
	currentSize := pm.QueueSize()
	if currentSize < pm.maxProxies/4 { // 当代理数量少于1/4时补充
		needCount := pm.maxProxies - currentSize
		return pm.FetchProxies(needCount)
	}
	return nil
}
