package config

import (
	"Ams/types"
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// LoadConfig 从YAML文件加载配置
func LoadConfig(configPath string) (*types.AppConfig, error) {
	// 如果配置文件不存在，创建默认配置文件
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("配置文件 %s 不存在，正在创建默认配置文件...\n", configPath)
		if err := CreateDefaultConfig(configPath); err != nil {
			return nil, fmt.Errorf("创建默认配置文件失败: %v", err)
		}
		fmt.Printf("默认配置文件已创建: %s\n", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	var config types.AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return &config, nil
}

// CreateDefaultConfig 创建默认配置文件
func CreateDefaultConfig(configPath string) error {
	defaultConfig := &types.AppConfig{
		Database: types.DatabaseConfig{
			Path: "db/amazon.db",
			Type: "json", // 默认使用JSON模式，避免CGO问题
		},
		Worker: types.WorkerConfig{
			NumThreads:       1,
			AccountsPerBatch: 1,
			StartIndex:       0,
			ProxyPerBatch:    100,
		},
		Server: types.ServerConfig{
			CodeServer: "http://*************:38787",
			ProxyAPI:   "https://acq.iemoapi.com/getProxyIp?lb=1&return_type=txt&protocol=http&num=",
		},
		URLs: types.URLConfig{
			Amazon:    "https://www.amazon.com/",
			Login:     "https://www.amazon.com/ap/signin",
			Captcha:   "https://www.amazon.com/errors/validateCaptcha",
			CVFVerify: "https://www.amazon.com/ap/cvf/verify",
		},
	}

	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 序列化为YAML
	data, err := yaml.Marshal(defaultConfig)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}

// validateConfig 验证配置
func validateConfig(config *types.AppConfig) error {
	// 验证数据库配置
	if config.Database.Path == "" {
		return fmt.Errorf("数据库路径不能为空")
	}
	
	if config.Database.Type != "sqlite" && config.Database.Type != "json" {
		return fmt.Errorf("数据库类型必须是 'sqlite' 或 'json'")
	}

	// 验证工作器配置
	if config.Worker.NumThreads <= 0 {
		return fmt.Errorf("线程数必须大于0")
	}
	
	if config.Worker.AccountsPerBatch <= 0 {
		return fmt.Errorf("每批账号数必须大于0")
	}
	
	if config.Worker.StartIndex < 0 {
		return fmt.Errorf("起始索引不能小于0")
	}
	
	if config.Worker.ProxyPerBatch <= 0 {
		return fmt.Errorf("代理池大小必须大于0")
	}

	// 验证服务器配置
	if config.Server.CodeServer == "" {
		return fmt.Errorf("验证码服务器地址不能为空")
	}
	
	if config.Server.ProxyAPI == "" {
		return fmt.Errorf("代理API地址不能为空")
	}

	// 验证URL配置
	if config.URLs.Amazon == "" {
		return fmt.Errorf("Amazon URL不能为空")
	}
	
	if config.URLs.Login == "" {
		return fmt.Errorf("登录URL不能为空")
	}

	return nil
}

// SaveConfig 保存配置到文件
func SaveConfig(config *types.AppConfig, configPath string) error {
	// 验证配置
	if err := validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 序列化为YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}
